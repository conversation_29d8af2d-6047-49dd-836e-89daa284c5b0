<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户注册 - 安徽春晟机械有限公司</title>
    <meta name="description" content="安徽春晟机械有限公司用户注册页面">
    <meta name="keywords" content="用户注册,安徽春晟机械">
    
    <!-- 样式文件 -->
    <link href="pcstyle.css" rel="stylesheet" type="text/css">
    <link href="reset.css" rel="stylesheet" type="text/css">
    <link href="32_Pc_zh-CN.css" rel="stylesheet" type="text/css">
    <link href="css/custom.css" rel="stylesheet" type="text/css">
    <link href="css/customer-service-chat.css" rel="stylesheet" type="text/css">

    <!-- Supabase -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script>
        // 检查 Supabase 是否加载成功，如果失败则显示错误信息
        window.addEventListener('load', function() {
            setTimeout(function() {
                if (typeof window.supabase === 'undefined') {
                    console.error('Supabase 库加载失败，可能是网络连接问题');
                    alert('网络连接异常，请检查网络设置或稍后重试。\n\n如果您在中国大陆，可能需要配置代理。');
                }
            }, 2000);
        });
    </script>
    
    <!-- jQuery -->
    <script src="jquery-3.6.3.min.js"></script>
</head>
<body>
    <!-- 头部区域 -->
    <header class="header-section">
        <!-- Logo区域 -->
        <div class="logo-section" style="background-color: rgb(37, 37, 37); padding: 10px 0;">
            <div class="container" style="width: 1200px; margin: 0 auto; display: flex; justify-content: space-between; align-items: center;">
                <div class="logo">
                    <a href="index.html">
                        <img src="16792504.png" alt="安徽春晟机械有限公司" style="height: 60px;">
                    </a>
                </div>
                <div class="user-actions">
                    <a href="login.html" class="login-btn" style="color: white; margin-right: 15px;">登录</a>
                    <a href="register.html" class="register-btn" style="background: #be131b; color: white; padding: 5px 15px;">注册</a>
                </div>
            </div>
        </div>
        
        <!-- 导航栏 -->
        <nav class="navigation" style="background-color: #F5F5F5; border-bottom: 1px solid #ddd;">
            <div class="container" style="width: 1200px; margin: 0 auto;">
                <ul class="nav-menu" style="display: flex; list-style: none; margin: 0; padding: 0;">
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="index.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">网站首页</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="about.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">关于我们</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="factory.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">企业展示</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="factory.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">工厂设备</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="products.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">产品中心</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="news.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">新闻中心</a>
                    </li>
                    <li style="flex: 1; text-align: center;">
                        <a href="contact.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">联系我们</a>
                    </li>
                </ul>
            </div>
        </nav>
    </header>

    <!-- 页面标题 -->
    <section class="page-header" style="background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('16719971.jpg') center/cover; padding: 80px 0; color: white; text-align: center;">
        <div class="container" style="width: 1200px; margin: 0 auto;">
            <h1 style="font-size: 48px; margin-bottom: 20px;">用户注册</h1>
            <p style="font-size: 18px;">创建您的账户，享受专业的产品服务</p>
        </div>
    </section>

    <!-- 注册表单区域 -->
    <section class="register-section" style="padding: 80px 0; background: #f8f9fa;">
        <div class="container" style="width: 1200px; margin: 0 auto;">
            <div class="register-container" style="max-width: 600px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 5px 20px rgba(0,0,0,0.1);">
                <div class="register-header" style="text-align: center; margin-bottom: 30px;">
                    <h2 style="color: #333; margin-bottom: 10px;">创建新账户</h2>
                    <p style="color: #666;">请填写以下信息完成注册</p>
                </div>

                <form id="register-form" class="register-form">
                    <div class="form-row" style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                        <div class="form-group">
                            <label for="first-name" style="display: block; margin-bottom: 5px; color: #333; font-weight: bold;">姓</label>
                            <input type="text" id="first-name" name="firstName" required 
                                   style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 5px; font-size: 16px; outline: none; transition: border-color 0.3s;"
                                   placeholder="请输入您的姓">
                        </div>
                        <div class="form-group">
                            <label for="last-name" style="display: block; margin-bottom: 5px; color: #333; font-weight: bold;">名</label>
                            <input type="text" id="last-name" name="lastName" required 
                                   style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 5px; font-size: 16px; outline: none; transition: border-color 0.3s;"
                                   placeholder="请输入您的名">
                        </div>
                    </div>

                    <div class="form-group" style="margin-bottom: 20px;">
                        <label for="email" style="display: block; margin-bottom: 5px; color: #333; font-weight: bold;">邮箱地址</label>
                        <input type="email" id="email" name="email" required 
                               style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 5px; font-size: 16px; outline: none; transition: border-color 0.3s;"
                               placeholder="请输入您的邮箱地址">
                    </div>

                    <div class="form-group" style="margin-bottom: 20px;">
                        <label for="phone" style="display: block; margin-bottom: 5px; color: #333; font-weight: bold;">手机号码</label>
                        <input type="tel" id="phone" name="phone" required 
                               style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 5px; font-size: 16px; outline: none; transition: border-color 0.3s;"
                               placeholder="请输入您的手机号码">
                    </div>

                    <div class="form-group" style="margin-bottom: 20px;">
                        <label for="company" style="display: block; margin-bottom: 5px; color: #333; font-weight: bold;">公司名称</label>
                        <input type="text" id="company" name="company" 
                               style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 5px; font-size: 16px; outline: none; transition: border-color 0.3s;"
                               placeholder="请输入您的公司名称（可选）">
                    </div>

                    <div class="form-row" style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                        <div class="form-group">
                            <label for="password" style="display: block; margin-bottom: 5px; color: #333; font-weight: bold;">密码</label>
                            <input type="password" id="password" name="password" required 
                                   style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 5px; font-size: 16px; outline: none; transition: border-color 0.3s;"
                                   placeholder="请输入密码">
                        </div>
                        <div class="form-group">
                            <label for="confirm-password" style="display: block; margin-bottom: 5px; color: #333; font-weight: bold;">确认密码</label>
                            <input type="password" id="confirm-password" name="confirmPassword" required 
                                   style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 5px; font-size: 16px; outline: none; transition: border-color 0.3s;"
                                   placeholder="请再次输入密码">
                        </div>
                    </div>

                    <div class="form-group" style="margin-bottom: 20px;">
                        <label for="user-type" style="display: block; margin-bottom: 5px; color: #333; font-weight: bold;">申请权限等级</label>
                        <select id="user-type" name="userType" required
                                style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 5px; font-size: 16px; outline: none; transition: border-color 0.3s;">
                            <option value="">请选择权限等级</option>
                            <option value="premium">高级用户</option>
                            <option value="privileged">特许用户</option>
                        </select>
                    </div>

                    <!-- 验证码 -->
                    <div class="form-group" style="margin-bottom: 30px;">
                        <label for="captcha-input" style="display: block; margin-bottom: 5px; color: #333; font-weight: bold;">验证码</label>
                        <div style="display: flex; gap: 10px; align-items: center;">
                            <input type="text" id="captcha-input" name="captcha" required
                                   style="flex: 1; padding: 12px; border: 2px solid #ddd; border-radius: 5px; font-size: 16px; outline: none; transition: border-color 0.3s;"
                                   placeholder="请输入验证码" maxlength="4">
                            <canvas id="captcha-canvas" style="border: 1px solid #ddd; border-radius: 4px; cursor: pointer;" title="点击刷新验证码"></canvas>
                            <button type="button" id="refresh-captcha"
                                    style="padding: 12px 15px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 14px;">
                                刷新
                            </button>
                        </div>
                        <small style="color: #666; font-size: 12px; margin-top: 5px; display: block;">点击验证码图片或刷新按钮可更换验证码</small>
                    </div>

                    <div class="form-group" style="margin-bottom: 30px;">
                        <label style="display: flex; align-items: flex-start; gap: 10px; color: #666; line-height: 1.5;">
                            <input type="checkbox" id="agree-terms" name="agreeTerms" required style="margin-top: 3px;">
                            <span>我已阅读并同意 <a href="#" style="color: #be131b; text-decoration: none;">《用户协议》</a> 和 <a href="#" style="color: #be131b; text-decoration: none;">《隐私政策》</a></span>
                        </label>
                    </div>

                    <button type="submit" class="register-btn" 
                            style="width: 100%; padding: 15px; background: #be131b; color: white; border: none; border-radius: 5px; font-size: 16px; font-weight: bold; cursor: pointer; transition: background-color 0.3s;">
                        注册账户
                    </button>
                </form>

                <div class="register-footer" style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
                    <p style="color: #666; margin-bottom: 15px;">已有账户？</p>
                    <a href="login.html" style="color: #be131b; text-decoration: none; font-weight: bold;">立即登录</a>
                </div>

                <!-- 注册状态提示 -->
                <div id="register-message" class="register-message" style="margin-top: 20px; padding: 10px; border-radius: 5px; text-align: center; display: none;"></div>
            </div>
        </div>
    </section>



    <!-- 页脚 -->
    <footer style="background: #333; color: white; padding: 40px 0;">
        <div class="container" style="width: 1200px; margin: 0 auto; text-align: center;">
            <p>&copy; 2024 安徽春晟机械有限公司. 保留所有权利.</p>
            <p>地址：安徽省广德经济开发区 | 专业从事减震器冲压件设计与生产</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="js/captcha.js"></script>
    <script src="js/supabase-init.js"></script>
    <script src="js/supabase-auth.js"></script>
    <script src="js/main.js"></script>
    <script>
        // 初始化验证码
        let captcha;
        document.addEventListener('DOMContentLoaded', function() {
            captcha = window.CaptchaManager.create('captcha-canvas', {
                width: 120,
                height: 40,
                length: 4
            });

            // 刷新按钮事件
            document.getElementById('refresh-captcha').addEventListener('click', function() {
                captcha.refresh();
                document.getElementById('captcha-input').value = '';
            });
        });

        // 注册表单处理
        document.getElementById('register-form').addEventListener('submit', async function(e) {
            e.preventDefault();

            // 获取表单数据
            const formData = {
                firstName: document.getElementById('first-name').value,
                lastName: document.getElementById('last-name').value,
                email: document.getElementById('email').value,
                phone: document.getElementById('phone').value,
                company: document.getElementById('company').value,
                password: document.getElementById('password').value,
                confirmPassword: document.getElementById('confirm-password').value,
                userType: document.getElementById('user-type').value,
                agreeTerms: document.getElementById('agree-terms').checked,
                captcha: document.getElementById('captcha-input').value
            };

            // 验证表单
            if (!validateForm(formData)) {
                return;
            }
            
            // 显示加载状态
            showMessage('正在注册...', 'info');
            
            try {
                // 等待认证系统初始化
                if (!window.auth || !window.auth.isInitialized) {
                    await new Promise(resolve => {
                        window.addEventListener('authReady', resolve, { once: true });
                    });
                }

                // 使用Supabase认证注册
                await window.auth.signUp({
                    username: formData.username,
                    email: formData.email,
                    password: formData.password,
                    firstName: formData.firstName,
                    lastName: formData.lastName,
                    phone: formData.phone,
                    company: formData.company,
                    userType: formData.userType
                });

                showMessage('注册成功！请查看邮箱验证邮件。', 'success');

                // 清空表单
                document.getElementById('register-form').reset();

                // 延迟跳转到登录页
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 3000);

            } catch (error) {
                console.error('注册错误:', error);
                showMessage('注册失败: ' + error.message, 'error');
            }
        });
        
        // 表单验证
        function validateForm(data) {
            // 验证验证码
            if (!data.captcha) {
                showMessage('请输入验证码', 'error');
                return false;
            }

            if (!captcha.validate(data.captcha)) {
                showMessage('验证码错误，请重新输入', 'error');
                captcha.refresh(); // 刷新验证码
                document.getElementById('captcha-input').value = '';
                return false;
            }

            if (data.password !== data.confirmPassword) {
                showMessage('两次输入的密码不一致', 'error');
                return false;
            }

            if (data.password.length < 6) {
                showMessage('密码长度至少6位', 'error');
                return false;
            }

            if (!data.agreeTerms) {
                showMessage('请同意用户协议和隐私政策', 'error');
                return false;
            }

            // 验证邮箱格式
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(data.email)) {
                showMessage('请输入有效的邮箱地址', 'error');
                return false;
            }

            // 验证手机号格式
            const phoneRegex = /^1[3-9]\d{9}$/;
            if (!phoneRegex.test(data.phone)) {
                showMessage('请输入有效的手机号码', 'error');
                return false;
            }

            return true;
        }
        
        // 显示消息
        function showMessage(message, type) {
            const messageDiv = document.getElementById('register-message');
            messageDiv.textContent = message;
            messageDiv.style.display = 'block';
            
            // 根据类型设置样式
            messageDiv.className = 'register-message';
            switch(type) {
                case 'success':
                    messageDiv.style.backgroundColor = '#d4edda';
                    messageDiv.style.color = '#155724';
                    messageDiv.style.border = '1px solid #c3e6cb';
                    break;
                case 'error':
                    messageDiv.style.backgroundColor = '#f8d7da';
                    messageDiv.style.color = '#721c24';
                    messageDiv.style.border = '1px solid #f5c6cb';
                    break;
                case 'info':
                    messageDiv.style.backgroundColor = '#d1ecf1';
                    messageDiv.style.color = '#0c5460';
                    messageDiv.style.border = '1px solid #bee5eb';
                    break;
            }
        }
        
        // 输入框焦点效果
        document.querySelectorAll('input, select').forEach(input => {
            input.addEventListener('focus', function() {
                this.style.borderColor = '#be131b';
            });
            
            input.addEventListener('blur', function() {
                this.style.borderColor = '#ddd';
            });
        });
        
        // 注册按钮悬停效果
        document.querySelector('.register-btn').addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#a00e15';
        });
        
        document.querySelector('.register-btn').addEventListener('mouseleave', function() {
            this.style.backgroundColor = '#be131b';
        });
        

    </script>

    <!-- 客服系统脚本 -->
    <script src="js/customer-service-chat.js"></script>
</body>
</html>

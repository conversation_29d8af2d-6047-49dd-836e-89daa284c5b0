<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品中心 - 安徽春晟机械有限公司</title>
    <meta name="description" content="安徽春晟机械有限公司产品中心，专业生产各类减震器冲压件产品">
    <meta name="keywords" content="减震器冲压件,汽车零部件,精密冲压件">
    
    <!-- 样式文件 -->
    <link href="pcstyle.css" rel="stylesheet" type="text/css">
    <link href="reset.css" rel="stylesheet" type="text/css">
    <link href="32_Pc_zh-CN.css" rel="stylesheet" type="text/css">
    <link href="css/custom.css" rel="stylesheet" type="text/css">
    <link href="css/customer-service-chat.css" rel="stylesheet" type="text/css">

    <!-- 搜索框增强样式 -->
    <style>
        /* 修复搜索框文字显示问题 */
        #smart-search-input {
            color: #333 !important;
            background: white !important;
        }

        #smart-search-input::placeholder {
            color: #666 !important;
            opacity: 1 !important;
        }

        /* 搜索框悬停效果 */
        #smart-search-input:hover {
            border-color: #d32f2f !important;
            box-shadow: 0 0 0 3px rgba(190, 19, 27, 0.1) !important;
        }

        #smart-search-input:focus {
            color: #333 !important;
            background: white !important;
        }

        #smart-search-input:focus {
            border-color: #be131b !important;
            box-shadow: 0 0 0 4px rgba(190, 19, 27, 0.15) !important;
        }

        /* 搜索按钮悬停效果 */
        #smart-search-btn:hover {
            background: linear-gradient(135deg, #d32f2f 0%, #be131b 100%) !important;
            transform: translateY(-50%) scale(1.05) !important;
            box-shadow: 0 6px 20px rgba(190, 19, 27, 0.4) !important;
        }

        /* 选项卡悬停效果 */
        .search-options label:hover,
        .filter-options > div:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.12) !important;
        }

        /* 下拉框样式优化 */
        #tolerance-select,
        #category-filter,
        #material-filter {
            color: #333 !important;
            font-weight: 500 !important;
        }

        #tolerance-select option,
        #category-filter option,
        #material-filter option {
            color: #333 !important;
            background: white !important;
            padding: 8px !important;
        }

        /* 重置按钮悬停效果 */
        #reset-filters-btn:hover {
            background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4) !important;
        }

        /* 搜索历史标签样式 */
        .history-tag {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
            border: 2px solid #dee2e6 !important;
            padding: 6px 12px !important;
            border-radius: 15px !important;
            font-size: 13px !important;
            cursor: pointer !important;
            transition: all 0.3s ease !important;
            font-weight: 500 !important;
            color: #333 !important;
            text-decoration: none !important;
            display: inline-block !important;
        }

        .history-tag:hover {
            background: linear-gradient(135deg, #be131b 0%, #d32f2f 100%) !important;
            color: white !important;
            border-color: #be131b !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(190, 19, 27, 0.3);
        }

        /* 动画效果 */
        @keyframes searchPulse {
            0% { box-shadow: 0 0 0 0 rgba(190, 19, 27, 0.4); }
            70% { box-shadow: 0 0 0 10px rgba(190, 19, 27, 0); }
            100% { box-shadow: 0 0 0 0 rgba(190, 19, 27, 0); }
        }

        .search-pulse {
            animation: searchPulse 1.5s infinite;
        }
    </style>

    <!-- Supabase -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    
    <!-- jQuery -->
    <script src="jquery-3.6.3.min.js"></script>
</head>
<body>
    <!-- 头部区域 -->
    <header class="header-section">
        <!-- Logo区域 -->
        <div class="logo-section" style="background-color: rgb(37, 37, 37); padding: 10px 0;">
            <div class="container" style="width: 1200px; margin: 0 auto; display: flex; justify-content: space-between; align-items: center;">
                <div class="logo">
                    <img src="16792504.png" alt="安徽春晟机械有限公司" style="height: 60px;">
                </div>
                <div class="user-actions">
                    <a href="login.html" class="login-btn" style="color: white; margin-right: 15px;">登录</a>
                    <a href="register.html" class="register-btn" style="background: #be131b; color: white; padding: 5px 15px;">注册</a>
                </div>
            </div>
        </div>
        
        <!-- 导航栏 -->
        <nav class="navigation" style="background-color: #F5F5F5; border-bottom: 1px solid #ddd;">
            <div class="container" style="width: 1200px; margin: 0 auto;">
                <ul class="nav-menu" style="display: flex; list-style: none; margin: 0; padding: 0;">
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="index.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">网站首页</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="about.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">关于我们</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="factory.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">企业展示</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="factory.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">工厂设备</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="products.html" style="display: block; padding: 20px; color: white; background: #be131b; text-decoration: none; transition: all 0.3s;">产品中心</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="news.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">新闻中心</a>
                    </li>
                    <li style="flex: 1; text-align: center;">
                        <a href="contact.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">联系我们</a>
                    </li>
                </ul>
            </div>
        </nav>
    </header>

    <!-- 页面标题 -->
    <section class="page-header" style="background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('16719971.jpg') center/cover; padding: 80px 0; color: white; text-align: center;">
        <div class="container" style="width: 1200px; margin: 0 auto;">
            <h1 style="font-size: 48px; margin-bottom: 20px;">产品中心</h1>
            <p style="font-size: 18px;">专业生产各类减震器冲压件产品</p>
        </div>
    </section>



    <!-- 智能搜索 -->
    <section class="smart-search-section" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 50px 0; border-bottom: 1px solid #dee2e6;">
        <div class="container" style="width: 1200px; margin: 0 auto;">
            <!-- 搜索标题 -->
            <div style="text-align: center; margin-bottom: 40px;">
                <h2 style="font-size: 28px; color: #333; margin-bottom: 10px; font-weight: 600;">🔍 智能产品搜索</h2>
                <p style="color: #666; font-size: 16px;">支持特殊符号、近似数值匹配，快速找到您需要的产品</p>
            </div>

            <!-- 主搜索框 -->
            <div class="main-search" style="text-align: center; margin-bottom: 35px;">
                <div style="position: relative; display: inline-block; width: 100%; max-width: 700px; box-shadow: 0 8px 25px rgba(190, 19, 27, 0.15); border-radius: 50px; background: white;">
                    <input type="text" id="smart-search-input" placeholder="🔍 智能搜索：支持φ78、74±2等特殊符号和近似数值..."
                           style="width: 100%; padding: 20px 30px; border: 3px solid #be131b; border-radius: 50px; font-size: 16px; outline: none; background: white; transition: all 0.3s ease; box-shadow: inset 0 2px 5px rgba(0,0,0,0.05);">
                    <button id="smart-search-btn" style="position: absolute; right: 8px; top: 50%; transform: translateY(-50%); background: linear-gradient(135deg, #be131b 0%, #d32f2f 100%); color: white; border: none; padding: 12px 25px; border-radius: 40px; cursor: pointer; font-weight: 600; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(190, 19, 27, 0.3);">
                        🚀 搜索
                    </button>
                </div>
            </div>

            <!-- 搜索选项 -->
            <div class="search-options" style="display: flex; justify-content: center; gap: 25px; margin-bottom: 30px; flex-wrap: wrap;">
                <label style="display: flex; align-items: center; gap: 8px; background: white; padding: 10px 20px; border-radius: 25px; box-shadow: 0 2px 10px rgba(0,0,0,0.08); cursor: pointer; transition: all 0.3s ease;">
                    <input type="checkbox" id="include-approximate" checked style="width: 18px; height: 18px; accent-color: #be131b;">
                    <span style="font-weight: 500; color: #333;">📊 包含近似匹配</span>
                </label>
                <label style="display: flex; align-items: center; gap: 10px; background: white; padding: 10px 20px; border-radius: 25px; box-shadow: 0 2px 10px rgba(0,0,0,0.08);">
                    <span style="font-weight: 500; color: #333;">⚙️ 误差范围：</span>
                    <select id="tolerance-select" style="padding: 8px 12px; border: 2px solid #e9ecef; border-radius: 15px; background: white; color: #333; font-weight: 500; outline: none; cursor: pointer;">
                        <option value="1">±1</option>
                        <option value="2">±2</option>
                        <option value="3" selected>±3</option>
                        <option value="5">±5</option>
                        <option value="10">±10</option>
                    </select>
                </label>
            </div>

            <!-- 筛选选项 -->
            <div class="filter-options" style="display: flex; justify-content: center; gap: 20px; margin-bottom: 30px; flex-wrap: wrap;">
                <div style="display: flex; align-items: center; gap: 10px; background: white; padding: 12px 20px; border-radius: 25px; box-shadow: 0 2px 10px rgba(0,0,0,0.08);">
                    <span style="font-weight: 500; color: #333;">📂 类别：</span>
                    <select id="category-filter" style="padding: 8px 15px; border: 2px solid #e9ecef; border-radius: 15px; background: white; color: #333; font-weight: 500; outline: none; cursor: pointer;">
                        <option value="">所有类别</option>
                        <option value="ZJ">支架（座）类</option>
                        <option value="GD">固定圈（防护套）类</option>
                        <option value="ZE">支耳（板）类</option>
                        <option value="TP">弹簧盘类</option>
                        <option value="FC">防尘盖（顶板）类</option>
                        <option value="QT">其它类</option>
                        <!-- 兼容旧数据 -->
                        <option value="DB">DB类</option>
                        <option value="ZC">ZC类</option>
                    </select>
                </div>
                <div style="display: flex; align-items: center; gap: 10px; background: white; padding: 12px 20px; border-radius: 25px; box-shadow: 0 2px 10px rgba(0,0,0,0.08);">
                    <span style="font-weight: 500; color: #333;">🚗 车型：</span>
                    <select id="car-brand-filter" style="padding: 8px 15px; border: 2px solid #e9ecef; border-radius: 15px; background: white; color: #333; font-weight: 500; outline: none; cursor: pointer;">
                        <option value="">所有品牌</option>
                    </select>
                </div>
                <div style="display: flex; align-items: center; gap: 10px; background: white; padding: 12px 20px; border-radius: 25px; box-shadow: 0 2px 10px rgba(0,0,0,0.08);">
                    <span style="font-weight: 500; color: #333;">🚙 型号：</span>
                    <select id="car-model-filter" style="padding: 8px 15px; border: 2px solid #e9ecef; border-radius: 15px; background: white; color: #333; font-weight: 500; outline: none; cursor: pointer;">
                        <option value="">所有型号</option>
                    </select>
                </div>
                <div style="display: flex; align-items: center; gap: 10px; background: white; padding: 12px 20px; border-radius: 25px; box-shadow: 0 2px 10px rgba(0,0,0,0.08);">
                    <span style="font-weight: 500; color: #333;">🔧 材质：</span>
                    <select id="material-filter" style="padding: 8px 15px; border: 2px solid #e9ecef; border-radius: 15px; background: white; color: #333; font-weight: 500; outline: none; cursor: pointer;">
                        <option value="">所有材质</option>
                        <option value="SPHC">SPHC</option>
                        <option value="SPCC">SPCC</option>
                        <option value="Q235">Q235</option>
                    </select>
                </div>
                <button id="reset-filters-btn" style="padding: 12px 25px; background: linear-gradient(135deg, #6c757d 0%, #495057 100%); color: white; border: none; border-radius: 25px; font-weight: 600; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);">
                    🔄 重置筛选
                </button>
            </div>

            <!-- 搜索历史 -->
            <div id="search-history" style="margin-top: 25px; text-align: center; display: none;">
                <div style="color: #333 !important; font-size: 14px; margin-bottom: 15px; font-weight: 500;">🕒 搜索历史：</div>
                <div id="history-tags" style="display: flex; justify-content: center; gap: 10px; flex-wrap: wrap;"></div>
            </div>

            <!-- 搜索结果统计 -->
            <div id="search-stats" style="text-align: center; margin-top: 20px; padding: 15px; background: rgba(190, 19, 27, 0.1); border-radius: 20px; color: #be131b; font-size: 16px; font-weight: 600; display: none;"></div>
        </div>
    </section>

    <!-- 产品列表 -->
    <section class="products-section" style="padding: 60px 0; background: #f8f8f8;">
        <div class="container" style="width: 1200px; margin: 0 auto;">
            <div id="all-products-grid" class="products-grid" style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px;">
                <!-- 产品将通过JavaScript动态加载 -->
                <div class="loading">
                    <div class="spinner"></div>
                </div>
            </div>

            <!-- 分页 -->
            <div class="pagination" style="text-align: center; margin-top: 40px;">
                <button class="page-btn">上一页</button>
                <span class="page-numbers">
                    <button class="page-btn active">1</button>
                    <button class="page-btn">2</button>
                    <button class="page-btn">3</button>
                    <button class="page-btn">4</button>
                    <button class="page-btn">5</button>
                </span>
                <button class="page-btn">下一页</button>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer style="background: #333; color: white; padding: 40px 0;">
        <div class="container" style="width: 1200px; margin: 0 auto; text-align: center;">
            <p>&copy; 2024 安徽春晟机械有限公司. 保留所有权利.</p>
            <p>地址：安徽省广德经济开发区 | 专业从事减震器冲压件设计与生产</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="js/supabase-init.js"></script>
    <script src="js/supabase-auth.js"></script>
    <script src="js/smart-search.js?v=20250701-5"></script>
    <script src="js/main.js?v=20250701-5"></script>
    <script src="js/products.js?v=20250701-5"></script>

    <script>
        // 智能搜索相关函数已移至 js/products.js 中

        // 重置所有筛选
        function resetAllFilters() {
            document.getElementById('smart-search-input').value = '';
            document.getElementById('category-filter').value = '';
            document.getElementById('car-brand-filter').value = '';
            document.getElementById('car-model-filter').value = '';
            document.getElementById('material-filter').value = '';
            document.getElementById('include-approximate').checked = true;
            document.getElementById('tolerance-select').value = '3';

            // 重置车型选择器
            loadCarModels();

            // 显示所有产品
            loadAllProducts();

            // 隐藏搜索历史
            document.getElementById('search-history').style.display = 'none';

            // 清除统计信息
            document.getElementById('search-stats').textContent = '';
        }

        // 更新搜索统计
        function updateSearchStats(results, query) {
            const statsContainer = document.getElementById('search-stats');
            if (statsContainer) {
                const exactCount = results.exact.length;
                const approximateCount = results.approximate.length;
                const totalCount = exactCount + approximateCount;

                if (query && totalCount > 0) {
                    let statsText = `🎯 搜索"${query}"：找到 ${totalCount} 个结果`;
                    if (exactCount > 0 && approximateCount > 0) {
                        statsText += ` (精确匹配 ${exactCount} 个，相近推荐 ${approximateCount} 个)`;
                    }
                    statsContainer.textContent = statsText;
                    statsContainer.style.display = 'block';
                } else if (query && totalCount === 0) {
                    statsContainer.textContent = `😔 搜索"${query}"：未找到相关结果，请尝试其他关键词`;
                    statsContainer.style.display = 'block';
                    statsContainer.style.background = 'rgba(220, 53, 69, 0.1)';
                    statsContainer.style.color = '#dc3545';
                } else {
                    statsContainer.style.display = 'none';
                    statsContainer.style.background = 'rgba(190, 19, 27, 0.1)';
                    statsContainer.style.color = '#be131b';
                }
            }
        }

        // 显示搜索历史
        function showSearchHistory() {
            const historyContainer = document.getElementById('search-history');
            const historyTags = document.getElementById('history-tags');
            const history = smartSearch.getSearchHistory();

            if (history.length > 0) {
                let html = '';
                history.slice(0, 8).forEach(item => {
                    html += `
                        <span class="history-tag" onclick="useHistorySearch('${item}')">${item}</span>
                    `;
                });
                historyTags.innerHTML = html;
                historyContainer.style.display = 'block';
            }
        }

        // 使用历史搜索
        function useHistorySearch(query) {
            document.getElementById('smart-search-input').value = query;
            performSmartSearch();
        }

        // 加载车型数据
        async function loadCarModels() {
            try {
                const { data: carModels, error } = await supabase
                    .from('car_models')
                    .select('*')
                    .order('brand', { ascending: true });

                if (error) {
                    console.error('加载车型数据失败:', error);
                    return;
                }

                // 获取所有品牌
                const brands = [...new Set(carModels.map(model => model.brand))];

                // 填充品牌选择器
                const brandSelect = document.getElementById('car-brand-filter');
                brandSelect.innerHTML = '<option value="">所有品牌</option>';
                brands.forEach(brand => {
                    brandSelect.innerHTML += `<option value="${brand}">${brand}</option>`;
                });

                // 绑定品牌选择事件
                brandSelect.addEventListener('change', function() {
                    const selectedBrand = this.value;
                    const modelSelect = document.getElementById('car-model-filter');

                    // 重置型号选择器
                    modelSelect.innerHTML = '<option value="">所有型号</option>';

                    if (selectedBrand) {
                        // 筛选该品牌下的车型
                        const brandModels = carModels.filter(model => model.brand === selectedBrand);
                        brandModels.forEach(model => {
                            modelSelect.innerHTML += `<option value="${model.id}">${model.model}</option>`;
                        });
                    }

                    // 触发搜索
                    performSmartSearch();
                });

                // 绑定型号选择事件
                const modelSelect = document.getElementById('car-model-filter');
                modelSelect.addEventListener('change', function() {
                    performSmartSearch();
                });

                // 存储车型数据供其他函数使用
                window.carModelsData = carModels;

            } catch (error) {
                console.error('加载车型数据出错:', error);
            }
        }

        // 页面加载完成后初始化车型数据
        document.addEventListener('DOMContentLoaded', function() {
            loadCarModels();
        });
    </script>

    <!-- 客服系统脚本 -->
    <script src="js/customer-service-chat.js"></script>
</body>
</html>

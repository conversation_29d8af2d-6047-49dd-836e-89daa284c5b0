// 产品页面专用JavaScript

let allProducts = []; // 存储所有产品数据
let filteredProducts = []; // 存储筛选后的产品数据

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeProductsPage();
});

// 初始化产品页面
async function initializeProductsPage() {
    console.log('开始初始化产品页面...');

    // 加载所有产品
    await loadAllProductsData();

    // 初始化筛选功能
    initializeFilters();

    // 显示产品
    displayFilteredProducts();

    // 初始化智能搜索功能
    await initializeSmartProductSearch();
}

// 初始化智能产品搜索
async function initializeSmartProductSearch() {
    console.log('初始化智能搜索功能...');

    try {
        // 确保智能搜索对象存在
        if (typeof SmartSearch !== 'undefined') {
            window.smartSearch = new SmartSearch();
            smartSearch.setProducts(allProducts);
            console.log('智能搜索对象已创建，产品数据已设置');
        } else {
            console.warn('SmartSearch 类未找到，跳过智能搜索初始化');
        }

        // 初始化搜索界面
        if (typeof initializeSmartSearch === 'function') {
            initializeSmartSearch();
        }

        // 绑定智能搜索事件
        const smartSearchBtn = document.getElementById('smart-search-btn');
        const smartSearchInput = document.getElementById('smart-search-input');

        if (smartSearchBtn) {
            smartSearchBtn.addEventListener('click', performSmartSearch);
        }

        if (smartSearchInput) {
            smartSearchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performSmartSearch();
                }
            });

            // 实时搜索建议
            smartSearchInput.addEventListener('input', function() {
                const query = this.value.trim();
                if (query.length >= 2) {
                    if (typeof showSearchHistory === 'function') {
                        showSearchHistory();
                    }
                }
            });
        }

        // 绑定筛选器事件
        const categoryFilter = document.getElementById('category-filter');
        const materialFilter = document.getElementById('material-filter');
        const resetBtn = document.getElementById('reset-filters-btn');

        if (categoryFilter) categoryFilter.addEventListener('change', performSmartSearch);
        if (materialFilter) materialFilter.addEventListener('change', performSmartSearch);
        if (resetBtn) resetBtn.addEventListener('click', resetAllFilters);

        console.log('智能搜索功能初始化完成');
    } catch (error) {
        console.error('智能搜索初始化失败:', error);
    }
}

// 执行智能搜索（如果智能搜索模块不可用，则使用基本搜索）
function performSmartSearch() {
    try {
        if (typeof smartSearch !== 'undefined' && smartSearch.search) {
            // 使用智能搜索
            const query = document.getElementById('smart-search-input')?.value.trim() || '';
            const includeApproximate = document.getElementById('include-approximate')?.checked || false;
            const tolerance = parseInt(document.getElementById('tolerance-select')?.value || '3');
            const category = document.getElementById('category-filter')?.value || '';
            const material = document.getElementById('material-filter')?.value || '';

            console.log('执行智能搜索:', query);

            const results = smartSearch.search(query, {
                tolerance: tolerance,
                includeApproximate: includeApproximate
            });

            // 合并精确和近似结果
            let combinedResults = [...results.exact];
            if (includeApproximate) {
                combinedResults = combinedResults.concat(results.approximate);
            }

            // 应用传统筛选
            if (category || material) {
                combinedResults = combinedResults.filter(product => {
                    const categoryMatch = !category || product.product_category === category;
                    const materialMatch = !material || (product.material && product.material.includes(material));
                    return categoryMatch && materialMatch;
                });
            }

            // 显示智能搜索结果
            const container = document.getElementById('products-table-body');
            if (typeof displaySmartSearchResults === 'function') {
                displaySmartSearchResults(results, container);
            } else {
                // 回退到传统显示方式
                filteredProducts = combinedResults;
                displayFilteredProducts();
            }

            // 更新统计信息
            if (typeof updateSearchStats === 'function') {
                updateSearchStats(results, query);
            }

            // 显示搜索历史
            if (typeof showSearchHistory === 'function') {
                showSearchHistory();
            }

            console.log('智能搜索结果:', combinedResults.length, '个产品');
        } else {
            console.log('智能搜索不可用，使用基本搜索');
            applyFilters();
            displayFilteredProducts();
        }
    } catch (error) {
        console.error('智能搜索出错:', error);
        console.log('回退到基本搜索');
        applyFilters();
        displayFilteredProducts();
    }
}

// 重置所有筛选器
function resetAllFilters() {
    const smartSearchInput = document.getElementById('smart-search-input');
    const categoryFilter = document.getElementById('category-filter');
    const materialFilter = document.getElementById('material-filter');

    if (smartSearchInput) smartSearchInput.value = '';
    if (categoryFilter) categoryFilter.value = '';
    if (materialFilter) materialFilter.value = '';

    filteredProducts = [...allProducts];
    displayFilteredProducts();
}

// 加载所有产品数据
async function loadAllProductsData() {
    const container = document.getElementById('products-table-body');
    if (!container) {
        console.error('产品表格容器未找到');
        return;
    }

    // 显示加载状态
    container.innerHTML = `
        <tr>
            <td colspan="14" style="padding: 40px; text-align: center; color: #666;">
                <div style="font-size: 16px;">正在加载产品数据...</div>
            </td>
        </tr>
    `;

    try {
        console.log('开始加载产品数据...');

        // 使用已有的getProducts函数
        if (typeof getProducts === 'function') {
            allProducts = await getProducts();
        } else {
            console.error('getProducts函数未找到');
            allProducts = [];
        }

        console.log('加载到的产品数据:', allProducts);

        if (!allProducts || allProducts.length === 0) {
            container.innerHTML = `
                <tr>
                    <td colspan="14" style="padding: 40px; text-align: center; color: #666;">
                        暂无产品数据
                    </td>
                </tr>
            `;
            return;
        }

        filteredProducts = [...allProducts]; // 初始化筛选结果
        console.log('产品数据加载成功，共', allProducts.length, '个产品');

        // 动态加载材质选项
        loadMaterialOptions();
    } catch (error) {
        console.error('加载产品失败:', error);
        container.innerHTML = `
            <tr>
                <td colspan="14" style="padding: 40px; text-align: center; color: #666;">
                    加载产品失败，请稍后重试
                </td>
            </tr>
        `;
    }
}

// 初始化筛选功能
function initializeFilters() {
    const categoryFilter = document.getElementById('category-filter');
    const searchInput = document.getElementById('smart-search-input');
    const searchBtn = document.getElementById('smart-search-btn');
    const resetBtn = document.getElementById('reset-filters-btn');

    // 检查是否有来自首页的分类选择
    const selectedCategory = sessionStorage.getItem('selectedCategory');
    if (selectedCategory && categoryFilter) {
        // 设置分类筛选器的值
        categoryFilter.value = selectedCategory;
        console.log('从首页接收到分类筛选:', selectedCategory);

        // 清除sessionStorage中的分类选择
        sessionStorage.removeItem('selectedCategory');

        // 应用筛选
        setTimeout(() => {
            applyFilters();
        }, 100);
    }

    // 类别筛选事件
    if (categoryFilter) {
        categoryFilter.addEventListener('change', applyFilters);
    }

    // 搜索按钮事件
    if (searchBtn) {
        searchBtn.addEventListener('click', function() {
            performSmartSearch();
        });
    }

    // 搜索输入框事件
    if (searchInput) {
        // 回车事件
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSmartSearch();
            }
        });

        // 实时搜索
        searchInput.addEventListener('input', function() {
            clearTimeout(window.searchTimeout);
            window.searchTimeout = setTimeout(() => {
                applyFilters();
            }, 500);
        });
    }

    // 重置按钮事件
    if (resetBtn) {
        resetBtn.addEventListener('click', resetFilters);
    }

    // 材质筛选事件
    const materialFilter = document.getElementById('material-filter');
    if (materialFilter) {
        materialFilter.addEventListener('change', applyFilters);
    }

    // 车型筛选事件
    const carBrandFilter = document.getElementById('car-brand-filter');
    if (carBrandFilter) {
        carBrandFilter.addEventListener('change', applyFilters);
    }
}

// 应用筛选
function applyFilters() {
    const categoryFilter = document.getElementById('category-filter');
    const smartSearchInput = document.getElementById('smart-search-input');
    const materialFilter = document.getElementById('material-filter');
    const carBrandFilter = document.getElementById('car-brand-filter');

    const selectedCategory = categoryFilter ? categoryFilter.value : '';
    const searchTerm = smartSearchInput ? smartSearchInput.value.trim().toLowerCase() : '';
    const selectedMaterial = materialFilter ? materialFilter.value : '';
    const selectedCarBrand = carBrandFilter ? carBrandFilter.value : '';

    // 筛选产品
    filteredProducts = allProducts.filter(product => {
        // 类别筛选
        const categoryMatch = !selectedCategory || product.product_category === selectedCategory;

        // 材质筛选
        const materialMatch = !selectedMaterial || (product.material && product.material.includes(selectedMaterial));

        // 车型筛选
        const carBrandMatch = !selectedCarBrand ||
            (product.car_model && product.car_model.toLowerCase().includes(selectedCarBrand.toLowerCase())) ||
            (product.car_type && product.car_type.toLowerCase().includes(selectedCarBrand.toLowerCase())) ||
            (product.product_name && product.product_name.toLowerCase().includes(selectedCarBrand.toLowerCase()));

        // 搜索筛选 - 在所有可查询字段中搜索
        const searchMatch = !searchTerm ||
            // 产品编码
            (product.stock_code && product.stock_code.toLowerCase().includes(searchTerm)) ||
            (product.data_id && product.data_id.toLowerCase().includes(searchTerm)) ||
            // 产品名称
            (product.product_name && product.product_name.toLowerCase().includes(searchTerm)) ||
            // 产品类别
            (product.product_category && product.product_category.toLowerCase().includes(searchTerm)) ||
            // 产品规格
            (product.specifications && product.specifications.toLowerCase().includes(searchTerm)) ||
            // 材质
            (product.material && product.material.toLowerCase().includes(searchTerm)) ||
            // 料厚
            (product.material_thickness && product.material_thickness.toLowerCase().includes(searchTerm)) ||
            // 备注
            (product.notes && product.notes.toLowerCase().includes(searchTerm)) ||
            // 外形编码
            (product.shape_code && product.shape_code.toLowerCase().includes(searchTerm)) ||
            // 主要工艺1-4
            (product.main_process_1 && product.main_process_1.toLowerCase().includes(searchTerm)) ||
            (product.main_process_2 && product.main_process_2.toLowerCase().includes(searchTerm)) ||
            (product.main_process_3 && product.main_process_3.toLowerCase().includes(searchTerm)) ||
            (product.main_process_4 && product.main_process_4.toLowerCase().includes(searchTerm)) ||
            // 车型（如果有这个字段）
            (product.car_model && product.car_model.toLowerCase().includes(searchTerm)) ||
            (product.car_type && product.car_type.toLowerCase().includes(searchTerm));

        return categoryMatch && materialMatch && carBrandMatch && searchMatch;
    });

    // 显示筛选结果
    displayFilteredProducts();
}

// 重置筛选
function resetFilters() {
    const categoryFilter = document.getElementById('category-filter');
    const smartSearchInput = document.getElementById('smart-search-input');
    const materialFilter = document.getElementById('material-filter');
    const carBrandFilter = document.getElementById('car-brand-filter');

    if (categoryFilter) categoryFilter.value = '';
    if (smartSearchInput) smartSearchInput.value = '';
    if (materialFilter) materialFilter.value = '';
    if (carBrandFilter) carBrandFilter.value = '';

    filteredProducts = [...allProducts];
    displayFilteredProducts();
}

// 显示筛选后的产品
function displayFilteredProducts() {
    const container = document.getElementById('products-table-body');
    if (!container) {
        console.error('产品表格容器未找到');
        return;
    }

    console.log('开始显示产品，筛选后的产品数量:', filteredProducts.length);

    if (filteredProducts.length === 0) {
        container.innerHTML = `
            <tr>
                <td colspan="14" style="padding: 40px; text-align: center; color: #666;">
                    没有找到符合条件的产品
                </td>
            </tr>
        `;
        return;
    }

    // 生成产品表格行HTML
    console.log('生成产品表格行...');
    const productsHTML = filteredProducts.map(product => {
        console.log('处理产品:', product.product_name);
        return createProductTableRow(product);
    }).join('');

    console.log('设置产品HTML到表格容器');
    container.innerHTML = productsHTML;

    // 更新结果统计
    updateResultsCount();
}

// 创建产品表格行
function createProductTableRow(product) {
    const defaultImage = 'placeholder.svg';

    // 获取产品分类名称
    const categoryMapping = {
        'ZJ': '支架（座）类',
        'GD': '固定圈（防护套）类',
        'ZE': '支耳（板）类',
        'TP': '弹簧盘类',
        'FC': '防尘盖（顶板）类',
        'QT': '其它类',
        'DB': '防尘盖（顶板）类',
        'ZC': '支架（座）类'
    };

    return `
        <tr style="border-bottom: 1px solid #eee; font-size: 11px;" onmouseover="this.style.background='#f9f9f9'" onmouseout="this.style.background='white'">
            <td style="padding: 6px; border-right: 1px solid #eee; text-align: center;">
                ${product.stock_code || product.data_id || '无编码'}
            </td>
            <td style="padding: 6px; border-right: 1px solid #eee; text-align: center;">
                ${product.product_name || '未知产品'}
            </td>
            <td style="padding: 6px; border-right: 1px solid #eee; text-align: center;">
                ${categoryMapping[product.product_category] || product.product_category || '未分类'}
            </td>
            <td style="padding: 6px; border-right: 1px solid #eee; text-align: center;">
                ${product.specifications || '无规格'}
            </td>
            <td style="padding: 6px; border-right: 1px solid #eee; text-align: center;">
                ${product.material || '无'}
            </td>
            <td style="padding: 6px; border-right: 1px solid #eee; text-align: center;">
                ${product.material_thickness || '无'}
            </td>
            <td style="padding: 6px; border-right: 1px solid #eee; text-align: center;">
                ${product.notes || '无'}
            </td>
            <td style="padding: 6px; border-right: 1px solid #eee; text-align: center;">
                ${product.shape_code || '无'}
            </td>
            <td style="padding: 6px; border-right: 1px solid #eee; text-align: center;">
                ${product.main_process_1 || '无'}
            </td>
            <td style="padding: 6px; border-right: 1px solid #eee; text-align: center;">
                ${product.main_process_2 || '无'}
            </td>
            <td style="padding: 6px; border-right: 1px solid #eee; text-align: center;">
                ${product.main_process_3 || '无'}
            </td>
            <td style="padding: 6px; border-right: 1px solid #eee; text-align: center;">
                ${product.main_process_4 || '无'}
            </td>
            <td style="padding: 6px; border-right: 1px solid #eee; text-align: center;">
                <img src="${product.product_image || defaultImage}"
                     alt="${product.product_name}"
                     style="width: 40px; height: 40px; object-fit: contain; cursor: pointer; border: 1px solid #ddd;"
                     onclick="showImageModal('${product.product_image || defaultImage}', '${product.product_name}')"
                     onerror="this.src='${defaultImage}'">
            </td>
            <td style="padding: 6px; text-align: center;">
                <button onclick="viewProductDetail('${product.id}')"
                        style="padding: 4px 8px; background: #be131b; color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 11px;">
                    详情
                </button>
            </td>
        </tr>
    `;
}

// 显示图片模态框
function showImageModal(imageSrc, productName) {
    // 创建模态框
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
        cursor: pointer;
    `;

    modal.innerHTML = `
        <div style="max-width: 90%; max-height: 90%; background: white; padding: 20px; border-radius: 8px;">
            <h3 style="margin: 0 0 15px 0; text-align: center;">${productName}</h3>
            <img src="${imageSrc}" alt="${productName}" style="max-width: 100%; max-height: 70vh; object-fit: contain;">
            <p style="text-align: center; margin-top: 15px; color: #666;">点击任意位置关闭</p>
        </div>
    `;

    modal.onclick = function() {
        document.body.removeChild(modal);
    };

    document.body.appendChild(modal);
}

// 查看产品详情
function viewProductDetail(productId) {
    window.location.href = `product-detail.html?id=${productId}`;
}

// 全局函数：在表格中显示产品（供main.js调用）
window.displayProductsInTable = function(products) {
    allProducts = products || [];
    filteredProducts = [...allProducts];
    displayFilteredProducts();
};

// 更新结果统计
function updateResultsCount() {
    // 更新搜索结果统计
    const searchCountDisplay = document.getElementById('search-count-display');
    const currentPageDisplay = document.getElementById('current-page-display');
    const totalPagesDisplay = document.getElementById('total-pages-display');

    if (searchCountDisplay) {
        searchCountDisplay.textContent = filteredProducts.length;
    }

    if (currentPageDisplay) {
        currentPageDisplay.textContent = '1';
    }

    if (totalPagesDisplay) {
        const totalPages = Math.ceil(filteredProducts.length / 20); // 假设每页20个产品
        totalPagesDisplay.textContent = totalPages || 1;
    }

    // 可以在页面上显示筛选结果数量
    const totalCount = allProducts.length;
    const filteredCount = filteredProducts.length;
    
    // 如果有结果统计容器，更新它
    const statsContainer = document.getElementById('results-stats');
    if (statsContainer) {
        statsContainer.textContent = `显示 ${filteredCount} / ${totalCount} 个产品`;
    }
}

// 产品排序功能
function sortProducts(sortBy) {
    switch (sortBy) {
        case 'name':
            filteredProducts.sort((a, b) => a.product_name.localeCompare(b.product_name));
            break;
        case 'category':
            filteredProducts.sort((a, b) => a.product_category.localeCompare(b.product_category));
            break;
        case 'date':
            filteredProducts.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
            break;
        default:
            break;
    }
    
    displayFilteredProducts();
}

// 导出产品数据（管理员功能）
function exportProducts() {
    if (!currentUser || currentUserType !== 'admin') {
        alert('只有管理员可以导出产品数据');
        return;
    }
    
    // 创建CSV数据
    const headers = [
        '数据ID', '存货编码', '产品名称', '产品类别', '产品规格', '材质', '料厚',
        '备注', '外形编码', '主要工艺1', '主要工艺2', '主要工艺3', '主要工艺4',
        '工序数', '可变加工艺1', '可变加工艺2', '可变加工艺3'
    ];
    
    const csvContent = [
        headers.join(','),
        ...filteredProducts.map(product => [
            product.data_id,
            product.stock_code,
            product.product_name,
            product.product_category,
            product.specifications,
            product.material,
            product.thickness,
            product.remarks || '',
            product.shape_code || '',
            product.main_process_1 || '',
            product.main_process_2 || '',
            product.main_process_3 || '',
            product.main_process_4 || '',
            product.process_count || '',
            product.variable_process_1 || '',
            product.variable_process_2 || '',
            product.variable_process_3 || ''
        ].map(field => `"${field}"`).join(','))
    ].join('\n');
    
    // 下载CSV文件
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `产品数据_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 批量操作功能（管理员）
function initializeBatchOperations() {
    if (!currentUser || currentUserType !== 'admin') return;
    
    // 添加批量操作按钮
    const container = document.querySelector('.product-filters .filter-controls');
    if (container) {
        const batchHTML = `
            <div class="batch-operations" style="margin-left: auto; display: flex; gap: 10px;">
                <button id="select-all-btn" class="btn btn-secondary">全选</button>
                <button id="export-btn" class="btn">导出数据</button>
                <button id="batch-delete-btn" class="btn" style="background: #dc3545;">批量删除</button>
            </div>
        `;
        container.insertAdjacentHTML('beforeend', batchHTML);
        
        // 添加事件监听
        document.getElementById('select-all-btn').addEventListener('click', toggleSelectAll);
        document.getElementById('export-btn').addEventListener('click', exportProducts);
        document.getElementById('batch-delete-btn').addEventListener('click', batchDeleteProducts);
    }
}

// 全选/取消全选
function toggleSelectAll() {
    const checkboxes = document.querySelectorAll('.product-checkbox');
    const selectAllBtn = document.getElementById('select-all-btn');
    
    const allChecked = Array.from(checkboxes).every(cb => cb.checked);
    
    checkboxes.forEach(cb => {
        cb.checked = !allChecked;
    });
    
    selectAllBtn.textContent = allChecked ? '全选' : '取消全选';
}

// 批量删除产品
async function batchDeleteProducts() {
    const selectedIds = Array.from(document.querySelectorAll('.product-checkbox:checked'))
        .map(cb => cb.value);
    
    if (selectedIds.length === 0) {
        alert('请选择要删除的产品');
        return;
    }
    
    if (!confirm(`确定要删除选中的 ${selectedIds.length} 个产品吗？此操作不可恢复。`)) {
        return;
    }
    
    try {
        const { error } = await supabase
            .from('products')
            .delete()
            .in('id', selectedIds);
        
        if (error) throw error;
        
        alert('删除成功');
        await loadAllProductsData();
        displayFilteredProducts();
    } catch (error) {
        console.error('删除失败:', error);
        alert('删除失败，请稍后重试');
    }
}
